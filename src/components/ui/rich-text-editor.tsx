"use client";

import * as React from "react";
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import { MarkdownToolbar } from "@/components/ui/markdown-toolbar";
import { cn } from "@/lib/utils";

export interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  id?: string;
  disabled?: boolean;
  maxLength?: number; // Optional character limit for the markdown value
}

export const RichTextEditor = React.forwardRef<HTMLDivElement, RichTextEditorProps>(
  ({ value, onChange, placeholder, className, id, disabled = false, maxLength }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const [showMarkdown, setShowMarkdown] = React.useState(false);

    // Convert markdown to HTML for Tiptap
    const markdownToHtml = (markdown: string): string => {
      if (!markdown) return '<p></p>';

      // Split into lines for processing
      const lines = markdown.split('\n');
      let result = [];
      let inList = false;
      let listType = '';

      for (let line of lines) {
        // Check for list items
        const bulletMatch = line.match(/^- (.+)$/);
        const numberedMatch = line.match(/^\d+\. (.+)$/);

        if (bulletMatch) {
          if (!inList || listType !== 'ul') {
            if (inList) result.push(`</${listType}>`);
            result.push('<ul>');
            inList = true;
            listType = 'ul';
          }
          // Process formatting within list item
          let content = bulletMatch[1]
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
          result.push(`<li>${content}</li>`);
        } else if (numberedMatch) {
          if (!inList || listType !== 'ol') {
            if (inList) result.push(`</${listType}>`);
            result.push('<ol>');
            inList = true;
            listType = 'ol';
          }
          // Process formatting within list item
          let content = numberedMatch[1]
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
          result.push(`<li>${content}</li>`);
        } else {
          // Close any open list
          if (inList) {
            result.push(`</${listType}>`);
            inList = false;
            listType = '';
          }

          // Handle regular paragraphs
          if (line.trim()) {
            let content = line
              .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
              .replace(/\*(.*?)\*/g, '<em>$1</em>');
            result.push(`<p>${content}</p>`);
          } else {
            // Empty line - add paragraph break
            result.push('<p></p>');
          }
        }
      }

      // Close any remaining open list
      if (inList) {
        result.push(`</${listType}>`);
      }

      return result.join('') || '<p></p>';
    };

    // Convert HTML to markdown
    const htmlToMarkdown = (html: string): string => {
      if (!html || html === '<p></p>' || html === '<p><br></p>') return '';

      console.log('Converting HTML to markdown:', html);

      // Handle lists first - this is the most complex part
      let markdown = html;

      // Handle bullet lists
      markdown = markdown.replace(/<ul[^>]*>(.*?)<\/ul>/gs, (match, content) => {
        // Extract list items and convert to markdown
        const items = content.match(/<li[^>]*>(.*?)<\/li>/gs) || [];
        return items.map(item => {
          const itemContent = item.replace(/<li[^>]*>(.*?)<\/li>/s, '$1').trim();
          return `- ${itemContent}`;
        }).join('\n') + '\n';
      });

      // Handle numbered lists
      markdown = markdown.replace(/<ol[^>]*>(.*?)<\/ol>/gs, (match, content) => {
        // Extract list items and convert to markdown
        const items = content.match(/<li[^>]*>(.*?)<\/li>/gs) || [];
        return items.map((item, index) => {
          const itemContent = item.replace(/<li[^>]*>(.*?)<\/li>/s, '$1').trim();
          return `${index + 1}. ${itemContent}`;
        }).join('\n') + '\n';
      });

      // Handle formatting
      markdown = markdown
        .replace(/<strong[^>]*>(.*?)<\/strong>/g, '**$1**')
        .replace(/<em[^>]*>(.*?)<\/em>/g, '*$1*');

      // Handle paragraphs
      markdown = markdown
        .replace(/<p><br><\/p>/g, '\n')
        .replace(/<p>(.*?)<\/p>/g, '$1\n');

      // Handle line breaks
      markdown = markdown.replace(/<br\s*\/?>/g, '\n');

      // Clean up extra newlines and whitespace
      markdown = markdown
        .replace(/\n\s*\n/g, '\n\n')
        .replace(/\n+$/, '') // Remove trailing newlines
        .trim();

      console.log('Converted to markdown:', markdown);
      return markdown;
    };

    // Tiptap editor setup
    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          bulletList: {
            HTMLAttributes: {
              class: 'list-disc list-inside',
            },
          },
          orderedList: {
            HTMLAttributes: {
              class: 'list-decimal list-inside',
            },
          },
        }),
        Placeholder.configure({
          placeholder: placeholder || 'Start typing...',
        }),
      ],
      content: showMarkdown ? `<pre>${value}</pre>` : markdownToHtml(value),
      editable: !disabled,
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        if (showMarkdown) {
          // In markdown mode, get the raw text content
          const textContent = editor.getText();
          const next = typeof maxLength === 'number' && maxLength > 0
            ? textContent.slice(0, maxLength)
            : textContent;
          if (next !== value) onChange(next);
        } else {
          // In WYSIWYG mode, always convert HTML to markdown for storage
          const markdown = htmlToMarkdown(html);
          const next = typeof maxLength === 'number' && maxLength > 0
            ? markdown.slice(0, maxLength)
            : markdown;
          if (next !== value) onChange(next);
        }
      },
      onBlur: ({ editor }) => {
        // When editor loses focus, automatically switch back to WYSIWYG mode if in markdown mode
        if (showMarkdown) {
          console.log('Editor onBlur - switching from markdown to WYSIWYG mode');
          const currentContent = editor.getText();
          setShowMarkdown(false);

          // Update content for WYSIWYG mode
          setTimeout(() => {
            if (editor) {
              const htmlContent = markdownToHtml(currentContent);
              editor.commands.setContent(htmlContent);
            }
          }, 0);
        }
        setIsFocused(false);
      },
      onFocus: () => setIsFocused(true),
      editorProps: {
        attributes: {
          class: cn(
            'prose prose-sm max-w-none focus:outline-none',
            'min-h-[32px] px-3 py-2 text-xs',
            'prose-headings:font-normal prose-headings:text-foreground',
            'prose-p:text-foreground prose-p:my-0',
            'prose-li:text-foreground prose-li:my-0',
            'prose-strong:text-foreground prose-strong:font-semibold',
            'prose-em:text-foreground'
          ),
        },
      },
    });

    // Update editor content when value or mode changes
    React.useEffect(() => {
      if (editor && !editor.isFocused) {
        let newContent;

        if (showMarkdown) {
          newContent = `<pre>${value}</pre>`;
        } else {
          // Check if the value is already HTML (from list buttons) or markdown
          const isHtml = /<[^>]*>/.test(value);

          if (isHtml) {
            // If it's HTML, use it directly but clean it up
            newContent = value
              .replace(/<ul[^>]*>/g, '<ul>')
              .replace(/<ol[^>]*>/g, '<ol>')
              .replace(/<li[^>]*>/g, '<li>');
          } else {
            // If it's markdown, convert to HTML
            newContent = markdownToHtml(value);
          }
        }

        if (editor.getHTML() !== newContent) {
          editor.commands.setContent(newContent);
        }
      }
    }, [editor, value, showMarkdown]);

    const handleFormat = React.useCallback((format: string, prefix: string, suffix?: string) => {
      if (!editor) return;

      if (showMarkdown) {
        // In markdown mode, insert raw markdown
        const { from, to } = editor.state.selection;
        const selectedText = editor.state.doc.textBetween(from, to);
        
        if (format === "bold") {
          const replacement = selectedText ? `**${selectedText}**` : '****';
          editor.commands.insertContentAt({ from, to }, replacement);
          if (!selectedText) {
            editor.commands.setTextSelection(from + 2);
          }
        } else if (format === "italic") {
          const replacement = selectedText ? `*${selectedText}*` : '**';
          editor.commands.insertContentAt({ from, to }, replacement);
          if (!selectedText) {
            editor.commands.setTextSelection(from + 1);
          }
        } else if (format === "bullet") {
          editor.commands.insertContent('- ');
        } else if (format === "numbered") {
          editor.commands.insertContent('1. ');
        }
      } else {
        // In WYSIWYG mode, use Tiptap commands
        if (format === "bold") {
          editor.chain().focus().toggleBold().run();
        } else if (format === "italic") {
          editor.chain().focus().toggleItalic().run();
        } else if (format === "bullet") {
          // Handle bullet list with proper cursor positioning
          const wasInList = editor.isActive('bulletList');
          editor.chain().focus().toggleBulletList().run();

          // If we just created a list, ensure cursor is positioned correctly
          if (!wasInList) {
            setTimeout(() => {
              const { state } = editor;
              const { selection } = state;
              const { $from } = selection;

              // Find the list item and position cursor at the start of its content
              for (let depth = $from.depth; depth > 0; depth--) {
                const node = $from.node(depth);
                if (node.type.name === 'listItem') {
                  const pos = $from.start(depth) + 1;
                  editor.commands.setTextSelection(pos);
                  break;
                }
              }
            }, 0);
          }
        } else if (format === "numbered") {
          // Handle numbered list with proper cursor positioning
          const wasInList = editor.isActive('orderedList');
          editor.chain().focus().toggleOrderedList().run();

          // If we just created a list, ensure cursor is positioned correctly
          if (!wasInList) {
            setTimeout(() => {
              const { state } = editor;
              const { selection } = state;
              const { $from } = selection;

              // Find the list item and position cursor at the start of its content
              for (let depth = $from.depth; depth > 0; depth--) {
                const node = $from.node(depth);
                if (node.type.name === 'listItem') {
                  const pos = $from.start(depth) + 1;
                  editor.commands.setTextSelection(pos);
                  break;
                }
              }
            }, 0);
          }
        }
      }
    }, [editor, showMarkdown]);

    const handleToggleMarkdown = React.useCallback(() => {
      if (!editor) return;
      
      const currentContent = showMarkdown ? editor.getText() : htmlToMarkdown(editor.getHTML());
      setShowMarkdown(!showMarkdown);
      
      // Update content for the new mode
      setTimeout(() => {
        if (editor) {
          const newContent = !showMarkdown ? `<pre>${currentContent}</pre>` : markdownToHtml(currentContent);
          editor.commands.setContent(newContent);
          editor.commands.focus();
        }
      }, 0);
    }, [editor, showMarkdown]);

    if (!editor) {
      return null;
    }

    return (
      <div ref={ref} className={cn("space-y-2", className)}>
        {/* Toolbar - only show when focused */}
        {isFocused && (
          <MarkdownToolbar
            onFormat={handleFormat}
            showMarkdown={showMarkdown}
            onToggleMarkdown={handleToggleMarkdown}
            className="animate-in fade-in-0 slide-in-from-top-1 duration-200"
          />
        )}
        
        {/* Editor */}
        <div
          className={cn(
            "rounded-md border border-input bg-transparent dark:bg-input/30",
            "focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]",
            "max-h-[200px] overflow-y-auto",
            showMarkdown && "font-mono text-xs"
          )}
          onWheel={(e) => {
            // Prevent modal scrolling when scrolling within the description field
            const target = e.currentTarget;
            const { scrollTop, scrollHeight, clientHeight } = target;

            // Check if we're at the top or bottom of the scrollable area
            const isAtTop = scrollTop === 0;
            const isAtBottom = scrollTop + clientHeight >= scrollHeight;

            // Only prevent propagation if we're not at the boundaries
            // or if we're scrolling in a direction that would stay within bounds
            if ((!isAtTop && e.deltaY < 0) || (!isAtBottom && e.deltaY > 0)) {
              e.stopPropagation();
            }
          }}
        >
          <EditorContent editor={editor} />
        </div>
      </div>
    );
  }
);

RichTextEditor.displayName = "RichTextEditor";
