"use client";

import * as React from "react";
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';

import { MarkdownToolbar } from "@/components/ui/markdown-toolbar";
import { markdownToHtml, htmlToMarkdown } from "@/lib/markdown-utils";
import { cn } from "@/lib/utils";

export interface EnhancedRichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  id?: string;
  disabled?: boolean;
  maxLength?: number;
  autoFocus?: boolean;
  minHeight?: string;
  maxHeight?: string;
}

export const EnhancedRichTextEditor = React.forwardRef<HTMLDivElement, EnhancedRichTextEditorProps>(
  ({ 
    value, 
    onChange, 
    placeholder, 
    className, 
    id, 
    disabled = false, 
    maxLength,
    autoFocus = false,
    minHeight = "32px",
    maxHeight = "200px"
  }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const [showMarkdown, setShowMarkdown] = React.useState(false);

    // Tiptap editor setup with StarterKit (includes most features we need)
    const editor = useEditor({
      extensions: [
        StarterKit,
        Placeholder.configure({
          placeholder: placeholder || 'Start typing...',
        }),
      ],
      
      content: showMarkdown ? `<pre class="whitespace-pre-wrap font-mono text-sm">${value}</pre>` : markdownToHtml(value),
      editable: !disabled,
      autofocus: autoFocus,
      
      onUpdate: ({ editor }) => {
        if (showMarkdown) {
          // In markdown mode, get the raw text content
          const textContent = editor.getText();
          const next = typeof maxLength === 'number' && maxLength > 0
            ? textContent.slice(0, maxLength)
            : textContent;
          if (next !== value) onChange(next);
        } else {
          // In WYSIWYG mode, convert HTML to markdown for storage
          const html = editor.getHTML();
          const markdown = htmlToMarkdown(html);
          const next = typeof maxLength === 'number' && maxLength > 0
            ? markdown.slice(0, maxLength)
            : markdown;
          if (next !== value) onChange(next);
        }
      },
      
      onFocus: () => setIsFocused(true),
      onBlur: () => setIsFocused(false),
      
      editorProps: {
        attributes: {
          class: cn(
            'prose prose-sm max-w-none focus:outline-none',
            `min-h-[${minHeight}] px-3 py-2 text-xs`,
            'prose-headings:font-semibold prose-headings:text-foreground prose-headings:mt-2 prose-headings:mb-1',
            'prose-p:text-foreground prose-p:my-1 prose-p:leading-relaxed',
            'prose-li:text-foreground prose-li:my-0',
            'prose-strong:text-foreground prose-strong:font-semibold',
            'prose-em:text-foreground prose-em:italic',
            'prose-code:text-foreground prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-xs',
            'prose-pre:bg-muted prose-pre:text-foreground',
            'prose-a:text-primary prose-a:no-underline hover:prose-a:underline',
            'prose-ul:my-2 prose-ol:my-2',
            'prose-li:marker:text-muted-foreground',
            // Mobile optimizations
            'touch-manipulation', // Optimize touch interactions
            'selection:bg-primary/20', // Better selection visibility
          ),
          role: 'textbox',
          'aria-label': placeholder || 'Rich text editor',
          'aria-multiline': 'true',
          'aria-describedby': id ? `${id}-description` : undefined,
          spellcheck: 'true',
          autocorrect: 'on',
          autocapitalize: 'sentences',
        },
        
        handleKeyDown: (view, event) => {
          // Handle keyboard shortcuts
          if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
              case 'b':
                event.preventDefault();
                editor?.chain().focus().toggleBold().run();
                return true;
              case 'i':
                event.preventDefault();
                editor?.chain().focus().toggleItalic().run();
                return true;
              case 'k':
                event.preventDefault();
                handleLinkToggle();
                return true;
              case 'e':
                event.preventDefault();
                editor?.chain().focus().toggleCode().run();
                return true;
            }
          }

          // Handle Tab for list indentation
          if (event.key === 'Tab') {
            if (editor?.isActive('listItem')) {
              event.preventDefault();
              if (event.shiftKey) {
                editor.chain().focus().liftListItem('listItem').run();
              } else {
                editor.chain().focus().sinkListItem('listItem').run();
              }
              return true;
            }
          }

          return false;
        },
      },
    });

    // Update editor content when value or mode changes
    React.useEffect(() => {
      if (editor && !editor.isFocused) {
        let newContent;
        
        if (showMarkdown) {
          newContent = `<pre class="whitespace-pre-wrap font-mono text-sm">${value}</pre>`;
        } else {
          newContent = markdownToHtml(value);
        }
        
        if (editor.getHTML() !== newContent) {
          editor.commands.setContent(newContent);
        }
      }
    }, [editor, value, showMarkdown]);

    // Handle format commands from toolbar
    const handleFormat = (format: string, prefix?: string, suffix?: string) => {
      if (!editor) return;

      if (showMarkdown) {
        // In markdown mode, insert raw markdown
        const { from, to } = editor.state.selection;
        const selectedText = editor.state.doc.textBetween(from, to);
        
        let replacement = '';
        let cursorOffset = 0;
        
        switch (format) {
          case 'bold':
            replacement = selectedText ? `**${selectedText}**` : '****';
            cursorOffset = selectedText ? 0 : 2;
            break;
          case 'italic':
            replacement = selectedText ? `*${selectedText}*` : '**';
            cursorOffset = selectedText ? 0 : 1;
            break;
          case 'strikethrough':
            replacement = selectedText ? `~~${selectedText}~~` : '~~~~';
            cursorOffset = selectedText ? 0 : 2;
            break;
          case 'code':
            replacement = selectedText ? `\`${selectedText}\`` : '``';
            cursorOffset = selectedText ? 0 : 1;
            break;
          case 'heading1':
            replacement = `# ${selectedText || 'Heading'}`;
            break;
          case 'heading2':
            replacement = `## ${selectedText || 'Heading'}`;
            break;
          case 'heading3':
            replacement = `### ${selectedText || 'Heading'}`;
            break;
          case 'bulletList':
            replacement = `- ${selectedText || ''}`;
            break;
          case 'orderedList':
            replacement = `1. ${selectedText || ''}`;
            break;
          case 'link':
            replacement = selectedText ? `[${selectedText}](url)` : '[text](url)';
            break;
          case 'codeBlock':
            replacement = selectedText ? `\`\`\`\n${selectedText}\n\`\`\`` : '```\ncode\n```';
            break;
          default:
            return;
        }
        
        editor.commands.insertContentAt({ from, to }, replacement);
        if (cursorOffset > 0 && !selectedText) {
          editor.commands.setTextSelection(from + cursorOffset);
        }
      } else {
        // In WYSIWYG mode, use Tiptap commands
        switch (format) {
          case 'bold':
            editor.chain().focus().toggleBold().run();
            break;
          case 'italic':
            editor.chain().focus().toggleItalic().run();
            break;
          case 'strikethrough':
            editor.chain().focus().toggleStrike().run();
            break;
          case 'code':
            editor.chain().focus().toggleCode().run();
            break;
          case 'heading1':
            editor.chain().focus().toggleHeading({ level: 1 }).run();
            break;
          case 'heading2':
            editor.chain().focus().toggleHeading({ level: 2 }).run();
            break;
          case 'heading3':
            editor.chain().focus().toggleHeading({ level: 3 }).run();
            break;
          case 'bulletList':
            editor.chain().focus().toggleBulletList().run();
            break;
          case 'orderedList':
            editor.chain().focus().toggleOrderedList().run();
            break;
          case 'link':
            handleLinkToggle();
            break;
          case 'codeBlock':
            editor.chain().focus().toggleCodeBlock().run();
            break;
        }
      }
    };

    const handleLinkToggle = () => {
      if (!editor) return;

      // For now, just insert markdown link syntax
      const url = window.prompt('Enter URL:');
      if (url) {
        const text = window.prompt('Enter link text:') || 'link';
        editor.chain().focus().insertContent(`[${text}](${url})`).run();
      }
    };

    const handleToggleMarkdown = () => {
      setShowMarkdown(!showMarkdown);
    };

    return (
      <div ref={ref} className={cn("space-y-2", className)}>
        {/* Screen reader description */}
        {id && (
          <div id={`${id}-description`} className="sr-only">
            Rich text editor with markdown support. Use keyboard shortcuts like Ctrl+B for bold, Ctrl+I for italic.
            Press Tab to indent lists, Shift+Tab to outdent. Type markdown syntax for auto-formatting.
          </div>
        )}

        {/* Toolbar - show when focused or on mobile when editor has content */}
        {(isFocused || (value && window.innerWidth < 768)) && (
          <MarkdownToolbar
            onFormat={handleFormat}
            showMarkdown={showMarkdown}
            onToggleMarkdown={handleToggleMarkdown}
            editor={editor}
            className={cn(
              "animate-in fade-in-0 slide-in-from-top-1 duration-200",
              // Mobile optimizations
              "md:flex-nowrap flex-wrap gap-1",
              "sticky top-0 z-10 bg-background/95 backdrop-blur-sm"
            )}
          />
        )}

        {/* Editor */}
        <div
          className={cn(
            "rounded-md border border-input bg-transparent dark:bg-input/30",
            "focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]",
            `max-h-[${maxHeight}] overflow-y-auto`,
            showMarkdown && "font-mono text-xs",
            // Mobile optimizations
            "touch-manipulation",
            // Better focus indicators for accessibility
            "focus-within:ring-offset-2 focus-within:ring-offset-background"
          )}
          style={{ minHeight, maxHeight }}
          // Touch event handlers for mobile
          onTouchStart={(e) => {
            // Prevent zoom on double tap for better UX
            e.currentTarget.style.touchAction = 'manipulation';
          }}
        >
          <EditorContent editor={editor} />
        </div>

        {/* Character count and status */}
        <div className="flex justify-between items-center text-xs text-muted-foreground">
          {/* Keyboard shortcuts hint */}
          <div className="hidden md:block">
            {isFocused && (
              <span>
                Ctrl+B bold, Ctrl+I italic, Ctrl+K link, Tab indent
              </span>
            )}
          </div>

          {/* Character count */}
          {maxLength && (
            <div className={cn(
              "tabular-nums",
              value.length > maxLength * 0.9 && "text-warning",
              value.length >= maxLength && "text-destructive"
            )}>
              {value.length}/{maxLength}
            </div>
          )}
        </div>
      </div>
    );
  }
);

EnhancedRichTextEditor.displayName = "EnhancedRichTextEditor";
