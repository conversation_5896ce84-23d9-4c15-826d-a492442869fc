"use client";

import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Code,
  Code2,
  Link,
  Strikethrough,
  Heading1,
  Heading2,
  Heading3,
  Quote
} from "lucide-react";
import { cn } from "@/lib/utils";

export interface MarkdownToolbarProps {
  onFormat: (format: string, prefix?: string, suffix?: string) => void;
  showMarkdown: boolean;
  onToggleMarkdown: () => void;
  className?: string;
  editor?: any; // Tiptap editor instance
}

export function MarkdownToolbar({ onFormat, showMarkdown, onToggleMarkdown, className, editor }: MarkdownToolbarProps) {
  const toolbarButtons = [
    {
      icon: Bold,
      label: "Bold (Ctrl+B)",
      format: "bold",
      prefix: "**",
      suffix: "**",
      shortcut: "Ctrl+B",
    },
    {
      icon: Italic,
      label: "Italic (Ctrl+I)",
      format: "italic",
      prefix: "*",
      suffix: "*",
      shortcut: "Ctrl+I",
    },
    {
      icon: Strikethrough,
      label: "Strikethrough",
      format: "strikethrough",
      prefix: "~~",
      suffix: "~~",
    },
    {
      icon: Code2,
      label: "Inline Code",
      format: "code",
      prefix: "`",
      suffix: "`",
    },
  ];

  const headerButtons = [
    {
      icon: Heading1,
      label: "Heading 1",
      format: "heading1",
      prefix: "# ",
    },
    {
      icon: Heading2,
      label: "Heading 2",
      format: "heading2",
      prefix: "## ",
    },
    {
      icon: Heading3,
      label: "Heading 3",
      format: "heading3",
      prefix: "### ",
    },
  ];

  const listButtons = [
    {
      icon: List,
      label: "Bullet List",
      format: "bulletList",
      prefix: "- ",
    },
    {
      icon: ListOrdered,
      label: "Numbered List",
      format: "orderedList",
      prefix: "1. ",
    },
  ];

  const advancedButtons = [
    {
      icon: Link,
      label: "Link",
      format: "link",
      prefix: "[",
      suffix: "](url)",
    },
    {
      icon: Code,
      label: "Code Block",
      format: "codeBlock",
      prefix: "```\n",
      suffix: "\n```",
    },
  ];

  const renderButtonGroup = (buttons: typeof toolbarButtons, groupKey: string) => (
    <React.Fragment key={groupKey}>
      {buttons.map((button) => {
        const Icon = button.icon;
        const isActive = editor && (
          (button.format === 'bold' && editor.isActive('bold')) ||
          (button.format === 'italic' && editor.isActive('italic')) ||
          (button.format === 'strikethrough' && editor.isActive('strike')) ||
          (button.format === 'code' && editor.isActive('code')) ||
          (button.format === 'bulletList' && editor.isActive('bulletList')) ||
          (button.format === 'orderedList' && editor.isActive('orderedList')) ||
          (button.format === 'heading1' && editor.isActive('heading', { level: 1 })) ||
          (button.format === 'heading2' && editor.isActive('heading', { level: 2 })) ||
          (button.format === 'heading3' && editor.isActive('heading', { level: 3 })) ||
          (button.format === 'codeBlock' && editor.isActive('codeBlock'))
        );

        return (
          <Button
            key={button.format}
            variant="ghost"
            size="sm"
            className={cn(
              "h-7 w-7 p-0 hover:bg-muted",
              isActive && "bg-muted text-foreground"
            )}
            onClick={() => onFormat(button.format, button.prefix, button.suffix)}
            onMouseDown={(e) => e.preventDefault()}
            type="button"
            aria-label={button.label}
            title={button.label}
            data-toolbar="true"
          >
            <Icon className="h-3.5 w-3.5" />
          </Button>
        );
      })}
    </React.Fragment>
  );

  return (
    <div
      data-toolbar="true"
      className={cn(
        "flex items-center gap-1 p-2 border border-input bg-background rounded-md shadow-sm flex-wrap",
        className
      )}
    >
      {/* Text formatting */}
      {renderButtonGroup(toolbarButtons, 'text')}

      {/* Separator */}
      <div className="w-px h-4 bg-border mx-1" />

      {/* Headers */}
      {renderButtonGroup(headerButtons, 'headers')}

      {/* Separator */}
      <div className="w-px h-4 bg-border mx-1" />

      {/* Lists */}
      {renderButtonGroup(listButtons, 'lists')}

      {/* Separator */}
      <div className="w-px h-4 bg-border mx-1" />

      {/* Advanced */}
      {renderButtonGroup(advancedButtons, 'advanced')}

      {/* Separator */}
      <div className="w-px h-4 bg-border mx-1" />

      {/* Markdown toggle button */}
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "h-7 w-7 p-0 hover:bg-muted",
          showMarkdown && "bg-muted"
        )}
        onClick={onToggleMarkdown}
        onMouseDown={(e) => e.preventDefault()}
        type="button"
        aria-label={showMarkdown ? "Hide markdown syntax" : "Show markdown syntax"}
        title={showMarkdown ? "Hide markdown syntax" : "Show markdown syntax"}
        data-toolbar="true"
      >
        <Code className="h-3.5 w-3.5" />
      </Button>
    </div>
  );
}
