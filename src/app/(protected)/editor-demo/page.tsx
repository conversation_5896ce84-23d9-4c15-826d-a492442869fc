"use client";

import React, { useState } from 'react';
import { EnhancedRichTextEditor } from '@/components/ui/enhanced-rich-text-editor';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export default function EditorDemoPage() {
  const [enhancedValue, setEnhancedValue] = useState(`# Enhanced Rich Text Editor Demo

Welcome to the **enhanced** rich text editor! This editor supports:

## Features

### Text Formatting
- **Bold text** (Ctrl+B)
- *Italic text* (Ctrl+I)
- ~~Strikethrough text~~
- \`Inline code\` (Ctrl+E)

### Lists
- Bullet point 1
- Bullet point 2
  - Nested item (use Tab to indent)
  - Another nested item

1. Numbered list item
2. Another numbered item
3. Third item

### Code Blocks
\`\`\`javascript
function hello() {
  console.log("Hello, world!");
}
\`\`\`

### Links and More
Check out [this link](https://example.com) for more information.

> This is a blockquote (coming soon!)

Try the keyboard shortcuts:
- Ctrl+B for **bold**
- Ctrl+I for *italic*
- Ctrl+K for links
- Tab/Shift+Tab for list indentation
- Type \`**text**\` for auto-formatting!`);

  const [originalValue, setOriginalValue] = useState(`# Original Editor

This is the original rich text editor for comparison.

**Bold** and *italic* text work, but with limited features.

- Basic bullet points
- Limited formatting options`);

  const [testValue, setTestValue] = useState('');

  const demoContent = {
    headers: '# Header 1\n## Header 2\n### Header 3',
    lists: '- Bullet item 1\n- Bullet item 2\n  - Nested item\n\n1. Numbered item\n2. Another item',
    formatting: '**Bold text**, *italic text*, ~~strikethrough~~, and `inline code`',
    codeBlock: '```javascript\nconst message = "Hello, world!";\nconsole.log(message);\n```',
    links: '[GitHub](https://github.com) and [Documentation](https://docs.example.com)',
    mixed: '# Project Overview\n\nThis project includes:\n\n- **Frontend**: React with TypeScript\n- **Backend**: Node.js API\n- **Database**: PostgreSQL\n\n## Getting Started\n\n1. Clone the repository\n2. Install dependencies: `npm install`\n3. Start development: `npm run dev`\n\n```bash\ngit clone https://github.com/user/repo.git\ncd repo\nnpm install\n```\n\nFor more information, visit the [documentation](https://docs.example.com).'
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Rich Text Editor Comparison</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Compare the enhanced rich text editor with professional markdown support 
          against the original implementation.
        </p>
      </div>

      <Tabs defaultValue="enhanced" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="enhanced">Enhanced Editor</TabsTrigger>
          <TabsTrigger value="original">Original Editor</TabsTrigger>
          <TabsTrigger value="playground">Playground</TabsTrigger>
        </TabsList>

        <TabsContent value="enhanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Enhanced Rich Text Editor
                <Badge variant="secondary">New</Badge>
              </CardTitle>
              <CardDescription>
                Professional markdown editor with auto-formatting, keyboard shortcuts, 
                and comprehensive markdown support.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <EnhancedRichTextEditor
                value={enhancedValue}
                onChange={setEnhancedValue}
                placeholder="Start typing with markdown support..."
                maxLength={5000}
                minHeight="200px"
                maxHeight="400px"
                autoFocus
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Features</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Keyboard Shortcuts</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>Ctrl+B - Bold</li>
                    <li>Ctrl+I - Italic</li>
                    <li>Ctrl+K - Link</li>
                    <li>Ctrl+E - Inline code</li>
                    <li>Tab/Shift+Tab - List indentation</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Auto-formatting</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>**text** → Bold</li>
                    <li>*text* → Italic</li>
                    <li>`code` → Inline code</li>
                    <li># → Header</li>
                    <li>- → Bullet list</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="original" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Original Rich Text Editor</CardTitle>
              <CardDescription>
                The current implementation with basic markdown support.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RichTextEditor
                value={originalValue}
                onChange={setOriginalValue}
                placeholder="Original editor with limited features..."
                maxLength={5000}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="playground" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Playground</CardTitle>
              <CardDescription>
                Test the enhanced editor with various markdown examples.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {Object.entries(demoContent).map(([key, content]) => (
                  <Button
                    key={key}
                    variant="outline"
                    size="sm"
                    onClick={() => setTestValue(content)}
                  >
                    {key.charAt(0).toUpperCase() + key.slice(1)}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTestValue('')}
                >
                  Clear
                </Button>
              </div>

              <EnhancedRichTextEditor
                value={testValue}
                onChange={setTestValue}
                placeholder="Try typing markdown syntax or click the buttons above..."
                maxLength={2000}
                minHeight="150px"
                maxHeight="300px"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Markdown Output</CardTitle>
              <CardDescription>
                The raw markdown that will be stored and is compatible with AI tools.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-md text-sm overflow-auto max-h-40">
                {testValue || 'No content yet...'}
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Implementation Benefits</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-semibold mb-2 text-green-600">✅ Enhanced Features</h4>
              <ul className="text-sm space-y-1">
                <li>Full markdown syntax support</li>
                <li>Professional keyboard shortcuts</li>
                <li>Auto-formatting as you type</li>
                <li>Advanced list management</li>
                <li>Better copy/paste handling</li>
                <li>Mobile optimizations</li>
                <li>Accessibility improvements</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2 text-blue-600">🔧 Technical Improvements</h4>
              <ul className="text-sm space-y-1">
                <li>Robust markdown parser</li>
                <li>Clean HTML to markdown conversion</li>
                <li>Proper nested list handling</li>
                <li>Smart paste from external apps</li>
                <li>Extensible architecture</li>
                <li>Comprehensive test coverage</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2 text-purple-600">🤖 AI Tool Compatibility</h4>
              <ul className="text-sm space-y-1">
                <li>Clean, standard markdown output</li>
                <li>Consistent formatting</li>
                <li>No HTML artifacts</li>
                <li>Proper structure preservation</li>
                <li>Easy content extraction</li>
                <li>Cross-platform compatibility</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
