ALTER TABLE "tasks" ALTER COLUMN "status" SET DEFAULT 'not_started';--> statement-breakpoint
ALTER TABLE "lists" ADD COLUMN "icon" text;--> statement-breakpoint
ALTER TABLE "tags" ADD COLUMN "space_id" text NOT NULL;--> statement-breakpoint
ALTER TABLE "tasks" ADD COLUMN "assigned_user_id" text;--> statement-breakpoint
ALTER TABLE "tasks" ADD COLUMN "archived_at" timestamp;--> statement-breakpoint
ALTER TABLE "tags" ADD CONSTRAINT "tags_space_id_spaces_id_fk" FOREIGN KEY ("space_id") REFERENCES "public"."spaces"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_assigned_user_id_users_id_fk" FOREIGN KEY ("assigned_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_tasks_assigned_user_id" ON "tasks" USING btree ("assigned_user_id");--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-select" ON "lists" TO authenticated USING ((
      "lists"."user_id" = auth.user_id()
      OR EXISTS (
        SELECT 1 FROM space_collaborators sc
        WHERE sc.space_id = "lists"."space_id"
          AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-insert" ON "lists" TO authenticated WITH CHECK ((
      "lists"."user_id" = auth.user_id()
      OR EXISTS (
        SELECT 1 FROM space_collaborators sc
        WHERE sc.space_id = "lists"."space_id"
          AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-update" ON "lists" TO authenticated USING ((
      "lists"."user_id" = auth.user_id()
      OR EXISTS (
        SELECT 1 FROM space_collaborators sc
        WHERE sc.space_id = "lists"."space_id"
          AND sc.user_id = auth.user_id()
      )
    )) WITH CHECK ((
      "lists"."user_id" = auth.user_id()
      OR EXISTS (
        SELECT 1 FROM space_collaborators sc
        WHERE sc.space_id = "lists"."space_id"
          AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-delete" ON "lists" TO authenticated USING ((
      "lists"."user_id" = auth.user_id()
      OR EXISTS (
        SELECT 1 FROM space_collaborators sc
        WHERE sc.space_id = "lists"."space_id"
          AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-select" ON "picklist_values" TO authenticated USING ((
      EXISTS (
        SELECT 1 FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "picklist_values"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-insert" ON "picklist_values" TO authenticated WITH CHECK ((
      EXISTS (
        SELECT 1 FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "picklist_values"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-update" ON "picklist_values" TO authenticated USING ((
      EXISTS (
        SELECT 1 FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "picklist_values"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    )) WITH CHECK ((
      EXISTS (
        SELECT 1 FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "picklist_values"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-delete" ON "picklist_values" TO authenticated USING ((
      EXISTS (
        SELECT 1 FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "picklist_values"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-select" ON "spaces" TO authenticated USING ((
      auth.user_id() = "spaces"."user_id"
      OR EXISTS (
        SELECT 1 FROM space_collaborators sc
        WHERE sc.space_id = "spaces"."id"
          AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-insert" ON "spaces" TO authenticated WITH CHECK ((
      auth.user_id() = "spaces"."user_id"
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-update" ON "spaces" TO authenticated USING ((
      auth.user_id() = "spaces"."user_id"
    )) WITH CHECK ((
      auth.user_id() = "spaces"."user_id"
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-delete" ON "spaces" TO authenticated USING ((
      auth.user_id() = "spaces"."user_id"
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-select" ON "tags" TO authenticated USING ((
      EXISTS (
        SELECT 1 FROM spaces s WHERE s.id = "tags"."space_id" AND s.user_id = auth.user_id()
      ) OR EXISTS (
        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = "tags"."space_id" AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-insert" ON "tags" TO authenticated WITH CHECK ((
      EXISTS (
        SELECT 1 FROM spaces s WHERE s.id = "tags"."space_id" AND s.user_id = auth.user_id()
      ) OR EXISTS (
        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = "tags"."space_id" AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-update" ON "tags" TO authenticated USING ((
      EXISTS (
        SELECT 1 FROM spaces s WHERE s.id = "tags"."space_id" AND s.user_id = auth.user_id()
      ) OR EXISTS (
        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = "tags"."space_id" AND sc.user_id = auth.user_id()
      )
    )) WITH CHECK ((
      EXISTS (
        SELECT 1 FROM spaces s WHERE s.id = "tags"."space_id" AND s.user_id = auth.user_id()
      ) OR EXISTS (
        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = "tags"."space_id" AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-delete" ON "tags" TO authenticated USING ((
      EXISTS (
        SELECT 1 FROM spaces s WHERE s.id = "tags"."space_id" AND s.user_id = auth.user_id()
      ) OR EXISTS (
        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = "tags"."space_id" AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-select" ON "task_picklist_selections" TO authenticated USING ((
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = "task_picklist_selections"."task_id"
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-insert" ON "task_picklist_selections" TO authenticated WITH CHECK ((
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = "task_picklist_selections"."task_id"
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
      AND EXISTS (
        SELECT 1
        FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "task_picklist_selections"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-update" ON "task_picklist_selections" TO authenticated USING ((
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = "task_picklist_selections"."task_id"
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
      AND EXISTS (
        SELECT 1
        FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "task_picklist_selections"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    )) WITH CHECK ((
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = "task_picklist_selections"."task_id"
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
      AND EXISTS (
        SELECT 1
        FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "task_picklist_selections"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-delete" ON "task_picklist_selections" TO authenticated USING ((
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = "task_picklist_selections"."task_id"
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
      AND EXISTS (
        SELECT 1
        FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "task_picklist_selections"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-select" ON "task_tags" TO authenticated USING ((
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = "task_tags"."task_id"
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-insert" ON "task_tags" TO authenticated WITH CHECK ((
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = "task_tags"."task_id"
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
      AND EXISTS (
        SELECT 1
        FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "task_tags"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-update" ON "task_tags" TO authenticated USING ((
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = "task_tags"."task_id"
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
      AND EXISTS (
        SELECT 1
        FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "task_tags"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    )) WITH CHECK ((
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = "task_tags"."task_id"
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
      AND EXISTS (
        SELECT 1
        FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "task_tags"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-delete" ON "task_tags" TO authenticated USING ((
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = "task_tags"."task_id"
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
      AND EXISTS (
        SELECT 1
        FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = "task_tags"."tag_id"
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-select" ON "tasks" TO authenticated USING ((
      "tasks"."user_id" = auth.user_id()
      OR EXISTS (
        SELECT 1
        FROM lists l
        JOIN space_collaborators sc ON sc.space_id = l.space_id
        WHERE l.id = "tasks"."list_id"
          AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-insert" ON "tasks" TO authenticated WITH CHECK ((
      "tasks"."user_id" = auth.user_id()
      OR EXISTS (
        SELECT 1
        FROM lists l
        JOIN space_collaborators sc ON sc.space_id = l.space_id
        WHERE l.id = "tasks"."list_id"
          AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-update" ON "tasks" TO authenticated USING ((
      "tasks"."user_id" = auth.user_id()
      OR EXISTS (
        SELECT 1
        FROM lists l
        JOIN space_collaborators sc ON sc.space_id = l.space_id
        WHERE l.id = "tasks"."list_id"
          AND sc.user_id = auth.user_id()
      )
    )) WITH CHECK ((
      "tasks"."user_id" = auth.user_id()
      OR EXISTS (
        SELECT 1
        FROM lists l
        JOIN space_collaborators sc ON sc.space_id = l.space_id
        WHERE l.id = "tasks"."list_id"
          AND sc.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
ALTER POLICY "crud-authenticated-policy-delete" ON "tasks" TO authenticated USING ((
      "tasks"."user_id" = auth.user_id()
      OR EXISTS (
        SELECT 1
        FROM lists l
        JOIN space_collaborators sc ON sc.space_id = l.space_id
        WHERE l.id = "tasks"."list_id"
          AND sc.user_id = auth.user_id()
      )
    ));